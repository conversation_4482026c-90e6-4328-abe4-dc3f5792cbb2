---
import Layout from '../layouts/Layout.astro';
import { getEntry } from 'astro:content';

const homepageContent = await getEntry('homepage', 'main');
const { contact } = homepageContent.data;
---

<Layout title="Contact | Nob Hokleng | Software Developer & System Architect">
  <section class="contact pt-32 pb-24 bg-white">
    <div class="container mx-auto px-5 max-w-6xl">
      <h1 class="text-4xl font-bold text-center mb-12 font-heading relative">
        Contact
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h1>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-15 items-start">
        <div class="contact-info bg-light p-10 rounded-2xl shadow-lg">
          <p class="mb-8 text-lg leading-relaxed text-text">
            {contact.introText}
          </p>
          
          <div class="space-y-8">
            <div class="contact-method">
              <h3 class="text-lg font-semibold text-secondary mb-4 font-heading">Professional Networks</h3>
              <div class="space-y-4">
                <a href={contact.social.linkedin} target="_blank" rel="noopener" class="social-link flex items-center gap-3 px-5 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-medium shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1" aria-label="Connect with me on LinkedIn">
                  <i class="fab fa-linkedin text-lg" aria-hidden="true"></i>
                  <span>LinkedIn</span>
                </a>
                <a href={contact.social.github} target="_blank" rel="noopener" class="social-link flex items-center gap-3 px-5 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-medium shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1" aria-label="View my projects on GitHub">
                  <i class="fab fa-github text-lg" aria-hidden="true"></i>
                  <span>GitHub</span>
                </a>
              </div>
            </div>
            
            <div class="contact-method">
              <h3 class="text-lg font-semibold text-secondary mb-4 font-heading">Response Time</h3>
              <p class="text-gray-600 text-sm leading-relaxed">{contact.responseTime}</p>
            </div>
          </div>
        </div>
        
        <div class="contact-form bg-white p-10 rounded-2xl shadow-lg">
          <h3 class="text-2xl font-bold text-secondary mb-5 font-heading">Get In Touch</h3>
          <p class="coming-soon bg-light border-2 border-dashed border-gray-200 p-8 rounded-xl text-center text-gray-500 italic">
            Contact form coming soon - In the meantime, feel free to reach out via LinkedIn or GitHub.
          </p>
          
          <div class="contact-preview mt-8">
            <div class="form-preview space-y-5">
              <div class="form-field">
                <label class="block font-semibold text-text text-sm mb-2">Name</label>
                <div class="field-placeholder p-3 border-2 border-gray-200 rounded-lg bg-light text-gray-500 italic min-h-12">Your name</div>
              </div>
              <div class="form-field">
                <label class="block font-semibold text-text text-sm mb-2">Email</label>
                <div class="field-placeholder p-3 border-2 border-gray-200 rounded-lg bg-light text-gray-500 italic min-h-12"><EMAIL></div>
              </div>
              <div class="form-field">
                <label class="block font-semibold text-text text-sm mb-2">Message</label>
                <div class="field-placeholder p-3 border-2 border-gray-200 rounded-lg bg-light text-gray-500 italic min-h-20">Your message...</div>
              </div>
              <button type="button" class="btn-disabled w-full px-7 py-3 bg-gray-300 text-gray-500 rounded-lg font-semibold cursor-not-allowed opacity-60" disabled aria-label="Contact form coming soon">
                Send Message (Coming Soon)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout> 