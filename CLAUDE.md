# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

NobiSite is a professional portfolio website built with modern Astro.js + Tailwind CSS stack. The project demonstrates comprehensive planning with extensive documentation and follows professional development practices. **Migration from static HTML/CSS/JS to Astro.js has been completed successfully.**

## Development Commands

```bash
# Development server
npm run dev         # Astro dev server with hot reload
npm start          # Same as dev

# Build
npm run build      # Production build with Astro

# Preview built site
npm run preview    # Serves built files with Astro preview

# Type checking
npm run check      # TypeScript checking for Astro components
```

## Architecture Overview

- **Framework**: Astro.js with TypeScript for static site generation
- **Styling**: Tailwind CSS with custom design system
- **Content**: MDX with frontmatter-based collections (portfolio projects)
- **Components**: Astro components for Header, Footer, ProjectCard
- **Type Safety**: Full TypeScript integration with strict null checks
- **Build Output**: Static files optimized for production deployment

### Content Collections Structure
```
src/content/
├── config.ts           # Zod schemas for content validation
└── portfolio/          # Project case studies (MDX)
```

### Component Architecture
```
src/
├── components/         # Reusable Astro components
├── layouts/           # Page layouts (Layout.astro)
├── pages/             # File-based routing
└── styles/            # Global CSS + Tailwind
```

## Design System

### Tailwind Configuration
- **Primary**: #3a86ff (blue)
- **Secondary**: #0a2463 (dark blue)  
- **Accent**: #ff9e00 (orange)
- **Fonts**: Montserrat (headings), Poppins (body), Fira Code (mono)

### Path Aliases
- `@/*` maps to `src/*` for clean imports

## Content Management

### Portfolio Schema
- Required: title, publishDate, problem, solution, technologies, role, results, heroImage
- Optional: repoUrl, liveUrl

## Build Process

Astro.js handles:
- Static site generation with file-based routing
- Asset optimization and hashing
- TypeScript compilation
- Tailwind CSS processing
- MDX content processing for portfolio projects

## Development Notes

### Migration Status
✅ **Migration Complete!** - Successfully migrated from static HTML/CSS/JS to modern Astro.js stack
- All pages converted to Astro components
- Portfolio content migrated to MDX collections
- Build system working perfectly
- TypeScript integration complete

### Quality Standards
- Target: 95+ Lighthouse performance score
- WCAG 2.1 AA accessibility compliance  
- TypeScript strict mode enabled
- Modern ES2022 target

### Documentation
Comprehensive project docs in `docs/` folder covering planning, technical specs, and migration guides.