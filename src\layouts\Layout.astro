---
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import '../styles/global.css';

interface Props {
  title?: string;
  description?: string;
  ogImage?: string;
}

const { 
  title = 'Nob Hokleng | Software Developer & System Architect',
  description = 'Software developer with experience in scalable systems. Building high-performance applications with modern architecture patterns and best practices.',
  ogImage = 'https://nobhokleng.dev/images/og-image.jpg'
} = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>{title}</title>
    <meta name="title" content={title}>
    <meta name="description" content={description}>
    <meta name="keywords" content="software developer, backend developer, system architecture, scalable systems, web development, programming">
    <meta name="author" content="Nob Hokleng">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nobhokleng.dev/">
    <meta property="og:title" content={title}>
    <meta property="og:description" content={description}>
    <meta property="og:image" content={ogImage}>

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://nobhokleng.dev/">
    <meta property="twitter:title" content={title}>
    <meta property="twitter:description" content={description}>
    <meta property="twitter:image" content={ogImage}>

    <!-- Canonical URL -->
    <link rel="canonical" href="https://nobhokleng.dev/">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦕</text></svg>">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="https://github.com">
    <link rel="dns-prefetch" href="https://linkedin.com">

    <!-- Stylesheets with optimized loading -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer"></noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family:Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet"></noscript>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Nob Hokleng",
        "jobTitle": "Software Developer",
        "description": "Software developer with experience in scalable systems and modern architecture patterns",
        "url": "https://nobhokleng.dev",
        "sameAs": [
            "https://github.com/Nobhokleng",
            "https://linkedin.com/in/nobhokleng"
        ],
        "knowsAbout": [
            "Software Development",
            "System Architecture",
            "Backend Development",
            "DevOps",
            "Scalable Systems"
        ]
    }
    </script>
  </head>
  <body>
    <Header />
    <main>
      <slot />
    </main>
    <Footer />
  </body>
</html> 