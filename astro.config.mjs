import { defineConfig } from 'astro/config';
import tailwind from "@astrojs/tailwind";
import mdx from "@astrojs/mdx";
import { loadEnv } from 'vite';

const env = loadEnv(process.env.NODE_ENV, process.cwd(), 'VITE_');

// https://astro.build/config
export default defineConfig({
  site: env.VITE_SITE_URL || 'http://localhost:4321',
  integrations: [
    tailwind({
      config: {
        applyBaseStyles: true
      }
    }),
    mdx()
  ],
  build: {
    assetsPrefix: env.VITE_ASSETS_PREFIX || '/',
    rollupOptions: {
      output: {
        entryFileNames: '[name].[hash].js',
        chunkFileNames: 'chunks/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash][extname]',
      },
    },
  },
}); 