<header role="banner" class="fixed w-full top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-50 shadow-sm">
  <div class="container mx-auto px-5 py-4 flex justify-between items-center max-w-6xl">
    <a href="/" class="logo text-2xl font-bold text-primary font-heading relative group" aria-label="Nob Hokleng - Home">
      NH
      <span class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-primary to-accent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
    </a>
    <nav role="navigation" aria-label="Main navigation">
      <ul class="flex space-x-8">
        <li><a href="/#about" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="About me section">About</a></li>
        <li><a href="/#portfolio" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="Portfolio projects section">Portfolio</a></li>
        <li><a href="/resume" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="Resume and experience section">Resume</a></li>
        <li><a href="/resources" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="Resources and RSS feed section">Resources</a></li>
        <li><a href="/contact" class="nav-link text-text font-medium transition-colors duration-300 hover:text-primary relative py-1" aria-label="Contact information section">Contact</a></li>
      </ul>
    </nav>
  </div>
</header>

<style>
.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #3a86ff, #ff9e00);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  transform: scaleX(1);
  transform-origin: left;
}

@media (max-width: 768px) {
  nav ul {
    display: none;
  }
}
</style> 