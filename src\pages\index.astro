---
import Layout from '../layouts/Layout.astro';
import ProjectCard from '../components/ProjectCard.astro';
import { getEntry } from 'astro:content';

const homepageContent = await getEntry('homepage', 'main');
const { hero, about } = homepageContent.data;
---

<Layout title="Nob Hokleng | Software Developer & System Architect">
  <!-- Hero Section -->
  <section class="hero pt-44 pb-28 bg-gradient-to-br from-light to-gray-100 relative overflow-hidden" role="banner">
    <div class="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-accent/10"></div>
    <div class="container mx-auto px-5 max-w-6xl relative z-10">
      <div class="text-center">
        <div class="construction-badge inline-block bg-gradient-to-r from-accent to-orange-400 text-black px-5 py-2 rounded-full font-semibold mb-10 shadow-lg animate-pulse">
          🚧 Site Under Construction 🚧
        </div>
        <h1 class="text-5xl md:text-6xl font-bold font-heading mb-4 bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent" set:html={hero.headline}>
        </h1>
        <h2 class="text-2xl md:text-3xl text-primary mb-6 font-semibold">
          {hero.subheadline}
        </h2>
        <p class="text-lg max-w-3xl mx-auto mb-10 text-text leading-relaxed">
          {hero.description}
        </p>
        
        <div class="hero-highlights flex justify-center gap-6 mb-10 flex-wrap">
          {hero.highlights.map((highlight) => (
            <div class="highlight-item flex items-center gap-3 px-5 py-3 bg-white/90 rounded-full shadow-md border border-primary/10 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <span class="text-xl">{highlight.icon}</span>
              <span class="font-semibold text-text text-sm">{highlight.label}</span>
            </div>
          ))}
        </div>
        
        <div class="cta-buttons flex justify-center gap-5 flex-col sm:flex-row">
          <a href={hero.primaryCTA.url} class="btn-primary inline-block px-7 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1" aria-label="View my portfolio projects">
            {hero.primaryCTA.text}
          </a>
          <a href={hero.secondaryCTA.url} class="btn-secondary inline-block px-7 py-3 border-2 border-primary text-primary rounded-lg font-semibold hover:bg-primary/5 transition-all duration-300 hover:-translate-y-1" aria-label="Get in touch with me">
            {hero.secondaryCTA.text}
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about py-24 bg-white" role="main">
    <div class="container mx-auto px-5 max-w-6xl">
      <h2 class="section-title text-4xl font-bold text-center mb-12 font-heading relative">
        About Me
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h2>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-15 items-start">
        <div class="about-text space-y-6">
          <p class="text-lg font-medium text-secondary leading-relaxed">
            {about.openingLine}
          </p>
          {about.mainContent.map((paragraph) => (
            <p class="text-base leading-relaxed text-text">
              {paragraph}
            </p>
          ))}
        </div>
          
          <div class="experience-highlights mt-8">
            <h3 class="text-xl font-bold text-secondary mb-5 font-heading">Experience Highlights</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Designed and implemented scalable backend systems</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Built high-performance APIs serving thousands of requests</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Implemented DevOps practices and CI/CD pipelines</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Worked with cloud platforms and containerization</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Mentored team members and led technical initiatives</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div class="skills-section bg-light p-10 rounded-2xl shadow-lg">
          <h3 class="text-2xl font-bold text-secondary mb-8 text-center font-heading">Technical Skills</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="skill-category bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <h4 class="text-base font-semibold text-primary mb-4 font-heading">Backend Development</h4>
              <ul class="space-y-1 text-sm text-text">
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Java & Spring Framework</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Node.js & Express</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Python & FastAPI</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>RESTful APIs & GraphQL</li>
              </ul>
            </div>
            <div class="skill-category bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <h4 class="text-base font-semibold text-primary mb-4 font-heading">System Architecture</h4>
              <ul class="space-y-1 text-sm text-text">
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Microservices Architecture</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Event-Driven Systems</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Database Design</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Caching Strategies</li>
              </ul>
            </div>
            <div class="skill-category bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <h4 class="text-base font-semibold text-primary mb-4 font-heading">DevOps & Infrastructure</h4>
              <ul class="space-y-1 text-sm text-text">
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Docker & Kubernetes</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>AWS & Cloud Services</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>CI/CD Pipelines</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Monitoring & Logging</li>
              </ul>
            </div>
            <div class="skill-category bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <h4 class="text-base font-semibold text-primary mb-4 font-heading">Development Practices</h4>
              <ul class="space-y-1 text-sm text-text">
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Test-Driven Development</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Code Review & Quality</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Agile Methodologies</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Technical Documentation</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="portfolio py-24 bg-light relative overflow-hidden" role="region" aria-labelledby="portfolio-title">
    <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5"></div>
    <div class="container mx-auto px-5 max-w-6xl relative z-10">
      <h2 id="portfolio-title" class="section-title text-4xl font-bold text-center mb-12 font-heading relative">
        Portfolio
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" role="list">
        <ProjectCard 
          title="E-Commerce Platform Backend"
          description="Scalable microservices architecture for high-traffic e-commerce platform. Built with modern technologies to handle thousands of concurrent users and process millions of transactions."
          tags={["Java", "Spring Boot", "Microservices"]}
        />
        <ProjectCard 
          title="Real-time Analytics System"
          description="Event-driven system processing millions of events daily. Provides real-time insights and analytics with low-latency data processing and visualization."
          tags={["Node.js", "Apache Kafka", "Redis"]}
        />
        <ProjectCard 
          title="DevOps Infrastructure"
          description="Automated CI/CD pipeline and infrastructure as code implementation. Streamlined deployment processes with monitoring, logging, and automated scaling."
          tags={["Docker", "Kubernetes", "AWS"]}
        />
      </div>
    </div>
  </section>
</Layout> 