{"permissions": {"allow": ["Bash(rm:*)", "Bash(npm run astro:dev:*)", "Bash(npm run astro:build:*)", "Bash(timeout 10 npm run astro:dev)", "<PERSON><PERSON>(true)", "Bash(npm run check:*)", "Bash(npm install:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(sed:*)", "Bash(npm run build:*)", "Bash(tree:*)", "mcp__zen__chat", "Bash(grep:*)", "Bash(rg:*)", "mcp__zen__analyze", "Bash(git commit:*)", "Bash(git config:*)", "Bash(git add:*)", "Bash(git reset:*)", "mcp__zen__codereview"], "deny": []}}